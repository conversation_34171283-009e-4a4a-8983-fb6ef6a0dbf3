/**
 * AI会话管理页面JavaScript代码
 * 处理会话列表的所有交互功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

$(document).ready(function() {
    let currentPage = 1;
    let pageSize = 10;
    let totalPages = 1;
    
    /**
     * 初始化页面
     */
    function init() {
        loadSessions();
        bindEvents();
    }
    
    /**
     * 绑定事件
     */
    function bindEvents() {
        // 刷新按钮
        $('#refreshBtn').click(function() {
            currentPage = 1;
            loadSessions();
        });
        
        // 清理过期按钮
        $('#cleanupBtn').click(function() {
            cleanupExpiredSessions();
        });
        
        // 搜索
        $('#searchBtn').click(function() {
            currentPage = 1;
            loadSessions();
        });
        
        // 回车搜索
        $('#searchInput').keydown(function(e) {
            if (e.which === 13) {
                currentPage = 1;
                loadSessions();
            }
        });
        
        // 筛选器变化
        $('#providerFilter, #dateFilter').change(function() {
            currentPage = 1;
            loadSessions();
        });
    }
    
    /**
     * 加载会话列表
     */
    function loadSessions() {
        const params = {
            page: currentPage,
            page_size: pageSize,
            search: $('#searchInput').val(),
            provider: $('#providerFilter').val(),
            date_filter: $('#dateFilter').val()
        };
        
        $.ajax({
            url: '/ai/getSessions',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(response) {
                if (response.st === 'success') {
                    renderSessions(response.data);
                    renderPagination(response.pagination || {});
                } else {
                    showError('加载会话列表失败：' + response.msg);
                }
            },
            error: function() {
                showError('网络错误，请稍后重试');
            }
        });
    }
    
    /**
     * 渲染会话列表
     */
    function renderSessions(sessions) {
        if (!sessions || sessions.length === 0) {
            $('#sessionsContainer').html(`
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h5>暂无会话记录</h5>
                    <p>开始与AI对话，创建你的第一个会话吧！</p>
                    <a href="/ai/chat" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 开始对话
                    </a>
                </div>
            `);
            return;
        }
        
        let html = '';
        sessions.forEach(function(session) {
            html += renderSessionItem(session);
        });
        
        $('#sessionsContainer').html(html);
        
        // 绑定会话操作事件
        bindSessionEvents();
    }
    
    /**
     * 渲染单个会话项
     */
    function renderSessionItem(session) {
        const providerColors = {
            'openai': 'success',
            'baidu': 'primary',
            'aliyun': 'warning',
            'zhipu': 'info',
            'deepseek': 'dark'
        };
        
        const providerColor = providerColors[session.provider] || 'secondary';
        
        return `
            <div class="session-item" data-session-id="${session.session_id}">
                <div class="session-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-comment-dots text-primary"></i>
                                会话 ${session.session_id.substr(0, 8)}...
                            </h6>
                            <div class="session-meta">
                                <span class="badge badge-${providerColor} provider-badge">
                                    ${session.provider.toUpperCase()}
                                </span>
                                <span class="ml-2">
                                    <i class="fas fa-clock"></i>
                                    ${formatDateTime(session.created_at)}
                                </span>
                                <span class="ml-2">
                                    <i class="fas fa-comments"></i>
                                    ${session.message_count} 条消息
                                </span>
                            </div>
                        </div>
                        <div class="session-actions">
                            <button class="btn btn-outline-primary btn-sm view-session" 
                                    data-session-id="${session.session_id}">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            <button class="btn btn-outline-success btn-sm continue-session" 
                                    data-session-id="${session.session_id}">
                                <i class="fas fa-play"></i> 继续
                            </button>
                            <button class="btn btn-outline-danger btn-sm delete-session" 
                                    data-session-id="${session.session_id}">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
                <div class="session-content">
                    ${renderMessagePreviews(session.recent_messages || [])}
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染消息预览
     */
    function renderMessagePreviews(messages) {
        if (!messages || messages.length === 0) {
            return '<p class="text-muted mb-0">暂无消息</p>';
        }
        
        let html = '';
        messages.slice(0, 3).forEach(function(message) {
            html += `
                <div class="message-preview user">
                    <strong>用户:</strong> ${truncateText(message.user_message, 100)}
                </div>
                <div class="message-preview assistant">
                    <strong>AI:</strong> ${truncateText(message.ai_response, 100)}
                </div>
            `;
        });
        
        if (messages.length > 3) {
            html += '<p class="text-muted mb-0 mt-2">... 还有更多消息</p>';
        }
        
        return html;
    }
    
    /**
     * 绑定会话操作事件
     */
    function bindSessionEvents() {
        // 查看会话
        $('.view-session').click(function() {
            const sessionId = $(this).data('session-id');
            viewSession(sessionId);
        });
        
        // 继续会话
        $('.continue-session').click(function() {
            const sessionId = $(this).data('session-id');
            continueSession(sessionId);
        });
        
        // 删除会话
        $('.delete-session').click(function() {
            const sessionId = $(this).data('session-id');
            deleteSession(sessionId);
        });
    }
    
    /**
     * 查看会话详情
     */
    function viewSession(sessionId) {
        window.open(`/ai/chat?session_id=${sessionId}`, '_blank');
    }
    
    /**
     * 继续会话
     */
    function continueSession(sessionId) {
        window.location.href = `/ai/chat?session_id=${sessionId}`;
    }
    
    /**
     * 删除会话
     */
    function deleteSession(sessionId) {
        Utils.showConfirm('确定要删除这个会话吗？删除后无法恢复。', function() {
            $.ajax({
                url: '/ai/deleteSession',
                type: 'POST',
                data: { session_id: sessionId },
                dataType: 'json',
                success: function(response) {
                    if (response.st === 'success') {
                        Utils.showSuccess('会话删除成功');
                        loadSessions(); // 重新加载列表
                    } else {
                        Utils.showError('删除失败：' + response.msg);
                    }
                },
                error: function() {
                    Utils.showError('网络错误，请稍后重试');
                }
            });
        });
    }
    
    /**
     * 清理过期会话
     */
    function cleanupExpiredSessions() {
        Utils.showConfirm('确定要清理30天前的过期会话吗？', function() {
            Utils.showSuccess('清理功能开发中...');
        });
    }
    
    /**
     * 渲染分页
     */
    function renderPagination(pagination) {
        if (!pagination.total_pages || pagination.total_pages <= 1) {
            $('#paginationContainer').hide();
            return;
        }
        
        totalPages = pagination.total_pages;
        currentPage = pagination.current_page || 1;
        
        let html = '';
        
        // 上一页
        if (currentPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a></li>`;
        }
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                html += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
            }
        }
        
        // 下一页
        if (currentPage < totalPages) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a></li>`;
        }
        
        $('#pagination').html(html);
        $('#paginationContainer').show();
        
        // 绑定分页事件
        $('.page-link').click(function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page && page !== currentPage) {
                currentPage = page;
                loadSessions();
            }
        });
    }
    
    /**
     * 工具函数
     */
    function formatDateTime(datetime) {
        return moment(datetime).format('YYYY-MM-DD HH:mm');
    }
    
    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    }
    
    function showError(message) {
        Utils.showError(message);
    }
    
    // 初始化
    init();
});
