/**
 * AI工具箱页面JavaScript代码
 * 处理文本分析工具的所有交互功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

$(document).ready(function() {
    let processingHistory = [];
    
    /**
     * 初始化页面
     */
    function init() {
        bindEvents();
        updateCharCount();
        clearAll();
    }
    
    /**
     * 绑定事件
     */
    function bindEvents() {
        // 处理按钮点击
        $('#processBtn').click(processText);
        
        // 复制结果
        $('#copyBtn').click(copyResult);
        
        // 清空内容
        $('#clearBtn').click(clearAll);
        
        // 文本输入字符计数
        $('#inputText').on('input', updateCharCount);
    }
    
    /**
     * 处理文本
     */
    function processText() {
        const text = $('#inputText').val().trim();
        const type = $('#toolType').val();
        const provider = $('#toolProvider').val();
        
        if (!text) {
            Utils.showWarning('请输入要处理的文本');
            return;
        }
        
        if (text.length > 5000) {
            Utils.showWarning('文本长度不能超过5000字符');
            return;
        }
        
        // 显示加载状态
        showLoading();
        $('#processBtn').prop('disabled', true);
        
        // 发送请求
        $.ajax({
            url: '/ai/analyzeText',
            type: 'POST',
            data: {
                text: text,
                type: type,
                provider: provider
            },
            dataType: 'json',
            success: function(response) {
                hideLoading();
                $('#processBtn').prop('disabled', false);
                
                if (response.st === 'success') {
                    showResult(response.data.response);
                    addToHistory(text, type, response.data.response, provider);
                    $('#copyBtn').prop('disabled', false);
                } else {
                    showError('处理失败：' + response.msg);
                }
            },
            error: function() {
                hideLoading();
                $('#processBtn').prop('disabled', false);
                showError('网络错误，请稍后重试');
            }
        });
    }
    
    /**
     * 显示加载状态
     */
    function showLoading() {
        $('#resultContainer').html(`
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-2">AI正在处理中，请稍候...</p>
            </div>
        `);
    }
    
    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        // 加载状态会被结果替换，无需特殊处理
    }
    
    /**
     * 显示处理结果
     */
    function showResult(result) {
        $('#resultContainer').html(`
            <div class="result-content">
                <h6 class="text-primary mb-2">
                    <i class="fas fa-check-circle"></i> 处理完成
                </h6>
                <div class="result-text">${result.replace(/\n/g, '<br>')}</div>
            </div>
        `);
    }
    
    /**
     * 显示错误信息
     */
    function showError(message) {
        $('#resultContainer').html(`
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        `);
    }
    
    /**
     * 复制结果到剪贴板
     */
    function copyResult() {
        const resultText = $('.result-text').text();
        if (resultText) {
            Utils.copyToClipboard(resultText);
        }
    }
    
    /**
     * 清空所有内容
     */
    function clearAll() {
        $('#inputText').val('');
        $('#resultContainer').html(`
            <div class="text-center text-muted">
                <i class="fas fa-robot fa-3x mb-3"></i>
                <p>请在左侧输入文本并选择处理类型，然后点击"开始处理"</p>
            </div>
        `);
        $('#copyBtn').prop('disabled', true);
        updateCharCount();
    }
    
    /**
     * 更新字符计数
     */
    function updateCharCount() {
        const text = $('#inputText').val();
        const count = text.length;
        const maxCount = 5000;
        
        let countHtml = `${count}/${maxCount}`;
        if (count > maxCount * 0.8) {
            countHtml = `<span class="text-warning">${countHtml}</span>`;
        }
        if (count >= maxCount) {
            countHtml = `<span class="text-danger">${countHtml}</span>`;
        }
        
        // 更新字符计数显示
        const charCountElement = $('.char-count');
        if (charCountElement.length > 0) {
            charCountElement.html(`最多输入5000字符 (${countHtml})`);
        }
    }
    
    /**
     * 添加到历史记录
     */
    function addToHistory(text, type, result, provider) {
        const typeNames = {
            'summary': '文本摘要',
            'sentiment': '情感分析',
            'keywords': '关键词提取',
            'translate': '中英翻译'
        };
        
        const historyItem = {
            text: text,
            type: type,
            typeName: typeNames[type],
            result: result,
            provider: provider,
            time: new Date().toLocaleString()
        };
        
        processingHistory.unshift(historyItem);
        
        // 只保留最近10条记录
        if (processingHistory.length > 10) {
            processingHistory = processingHistory.slice(0, 10);
        }
        
        renderHistory();
    }
    
    /**
     * 渲染历史记录
     */
    function renderHistory() {
        const historyContainer = $('#historyContainer');
        if (historyContainer.length === 0) return;
        
        if (processingHistory.length === 0) {
            historyContainer.html(`
                <div class="text-center text-muted">
                    <p>暂无处理历史</p>
                </div>
            `);
            return;
        }
        
        let html = '';
        processingHistory.forEach(function(item, index) {
            const shortText = item.text.length > 100 
                ? item.text.substring(0, 100) + '...' 
                : item.text;
            
            html += `
                <div class="history-item">
                    <div class="history-header">
                        <div>
                            <span class="history-type">${item.typeName}</span>
                            <small class="ml-2 text-muted">${item.provider.toUpperCase()}</small>
                        </div>
                        <small class="history-time">${item.time}</small>
                    </div>
                    <div class="history-content">
                        <strong>原文：</strong>${shortText}
                    </div>
                    <div class="history-result">
                        <strong>结果：</strong>${item.result.replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;
        });
        
        historyContainer.html(html);
    }
    
    // 初始化
    init();
});
