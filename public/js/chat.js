/**
 * AI聊天页面JavaScript代码
 * 处理聊天界面的所有交互功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

$(document).ready(function() {
    let currentSessionId = null;
    let isLoading = false;
    
    /**
     * 初始化页面
     */
    function init() {
        loadSessions();
        bindEvents();
        startNewChat();
    }
    
    /**
     * 绑定事件
     */
    function bindEvents() {
        // 发送按钮点击事件
        $('#sendBtn').click(sendMessage);
        
        // 回车发送消息
        $('#messageInput').keydown(function(e) {
            if (e.ctrlKey && e.which === 13) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 新建对话
        $('#newChatBtn').click(function() {
            startNewChat();
        });
        
        // AI模型切换
        $('#providerSelect').change(function() {
            const provider = $(this).val();
            appendSystemMessage(`已切换到 ${provider.toUpperCase()} 模型`);
        });
    }
    
    /**
     * 发送消息到AI
     */
    function sendMessage() {
        if (isLoading) {
            Utils.showWarning('请等待上一条消息处理完成');
            return;
        }
        
        const message = $('#messageInput').val().trim();
        if (!message) {
            Utils.showWarning('请输入消息内容');
            return;
        }
        
        if (message.length > 2000) {
            Utils.showWarning('消息长度不能超过2000字符');
            return;
        }
        
        // 显示用户消息
        appendMessage('user', message);
        $('#messageInput').val('');
        
        // 显示加载状态
        const loadingId = showTypingIndicator();
        isLoading = true;
        $('#sendBtn').prop('disabled', true);
        
        // 发送请求
        $.ajax({
            url: '/ai/sendMessage',
            type: 'POST',
            data: {
                message: message,
                session_id: currentSessionId,
                provider: $('#providerSelect').val()
            },
            dataType: 'json',
            success: function(response) {
                hideTypingIndicator(loadingId);
                isLoading = false;
                $('#sendBtn').prop('disabled', false);
                
                if (response.st === 'success') {
                    appendMessage('assistant', response.data.response);
                    currentSessionId = response.data.session_id;
                    updateSessionList();
                } else {
                    appendMessage('assistant', '抱歉，出现了错误：' + response.msg);
                }
            },
            error: function() {
                hideTypingIndicator(loadingId);
                isLoading = false;
                $('#sendBtn').prop('disabled', false);
                appendMessage('assistant', '网络错误，请稍后重试');
            }
        });
    }
    
    /**
     * 添加消息到聊天区域
     */
    function appendMessage(type, content, id = null) {
        const messageId = id || 'msg_' + Date.now();
        const messageHtml = `
            <div id="${messageId}" class="chat-message ${type}">
                <div class="message-content">${content}</div>
            </div>
        `;
        $('#chatContainer').append(messageHtml);
        scrollToBottom();
    }
    
    /**
     * 添加系统消息
     */
    function appendSystemMessage(content) {
        appendMessage('system', `<i class="fas fa-info-circle"></i> ${content}`);
    }
    
    /**
     * 显示打字指示器
     */
    function showTypingIndicator() {
        const loadingId = 'loading_' + Date.now();
        const typingHtml = `
            <div id="${loadingId}" class="chat-message loading">
                <div class="message-content">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    AI正在思考中...
                </div>
            </div>
        `;
        $('#chatContainer').append(typingHtml);
        scrollToBottom();
        return loadingId;
    }
    
    /**
     * 隐藏打字指示器
     */
    function hideTypingIndicator(loadingId) {
        $('#' + loadingId).remove();
    }
    
    /**
     * 滚动到底部
     */
    function scrollToBottom() {
        const container = $('#chatContainer');
        container.scrollTop(container[0].scrollHeight);
    }
    
    /**
     * 开始新对话
     */
    function startNewChat() {
        currentSessionId = null;
        $('#chatContainer').html(`
            <div class="chat-message system">
                <div class="message-content">
                    <i class="fas fa-robot text-primary"></i> 您好！我是AI助手，有什么可以帮助您的吗？
                </div>
            </div>
        `);
        $('.session-item').removeClass('active');
        appendSystemMessage('已开始新的对话');
    }
    
    /**
     * 加载会话列表
     */
    function loadSessions() {
        $.ajax({
            url: '/ai/getSessions',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.st === 'success') {
                    renderSessions(response.data);
                }
            }
        });
    }
    
    /**
     * 渲染会话列表
     */
    function renderSessions(sessions) {
        if (!sessions || sessions.length === 0) {
            $('#sessionList').html('<div class="text-muted text-center py-3">暂无会话记录</div>');
            return;
        }
        
        let html = '';
        sessions.forEach(function(session) {
            const title = session.first_message && session.first_message.length > 30 
                ? session.first_message.substring(0, 30) + '...' 
                : (session.first_message || '新对话');
            
            html += `
                <div class="session-item" data-session-id="${session.session_id}">
                    <div class="session-title">${title}</div>
                    <div class="session-time">${session.last_time || ''}</div>
                </div>
            `;
        });
        
        $('#sessionList').html(html);
        
        // 绑定会话点击事件
        $('.session-item').click(function() {
            const sessionId = $(this).data('session-id');
            loadSession(sessionId);
            $('.session-item').removeClass('active');
            $(this).addClass('active');
        });
    }
    
    /**
     * 加载指定会话
     */
    function loadSession(sessionId) {
        $.ajax({
            url: '/ai/getHistory',
            type: 'GET',
            data: { session_id: sessionId },
            dataType: 'json',
            success: function(response) {
                if (response.st === 'success') {
                    currentSessionId = sessionId;
                    renderChatHistory(response.data);
                }
            }
        });
    }
    
    /**
     * 渲染聊天历史
     */
    function renderChatHistory(history) {
        $('#chatContainer').html(`
            <div class="chat-message system">
                <div class="message-content">
                    <i class="fas fa-history"></i> 已加载历史对话
                </div>
            </div>
        `);
        
        if (history && history.length > 0) {
            history.forEach(function(item) {
                appendMessage('user', item.user_message);
                appendMessage('assistant', item.ai_response);
            });
        }
    }
    
    /**
     * 更新会话列表
     */
    function updateSessionList() {
        loadSessions();
    }
    
    // 初始化
    init();
});
