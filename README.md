# AI智能助手系统

这是一个基于PHP的AI聊天应用，支持多个AI服务提供商（OpenAI、百度文心一言、阿里通义千问、智谱AI等）。

## 功能特性

- 🤖 **多AI模型支持**: 支持OpenAI、百度、阿里云、智谱AI、DeepSeek等多个服务商
- 💬 **智能对话**: 实时AI聊天，支持上下文记忆
- 🛠️ **AI工具箱**: 文本摘要、情感分析、关键词提取、中英翻译
- 📝 **会话管理**: 聊天历史记录管理和会话切换
- 📱 **响应式设计**: 支持PC和移动端访问
- 🔒 **安全可靠**: 数据加密存储，支持软删除

## 系统要求

- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx Web服务器
- 支持URL重写

## 安装步骤

### 1. 环境准备

确保你的服务器已安装：
- PHP（建议7.4+）
- MySQL数据库
- Apache或Nginx Web服务器

### 2. 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE ai_chat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 导入数据库结构：
```bash
mysql -u root -p ai_chat < database/init.sql
```

3. 修改数据库配置文件 `app/Manage/Config/Database.php`：
```php
const DB_HOST = 'localhost';     // 数据库主机
const DB_NAME = 'ai_chat';       // 数据库名
const DB_USER = 'root';          // 数据库用户名
const DB_PASS = '';              // 数据库密码
```

### 3. AI服务配置

修改 `app/Manage/Config/AiConfig.php` 文件，配置你的AI服务API密钥：

```php
const PROVIDERS = [
    'openai' => [
        'api_key' => 'your-openai-api-key',
        'base_url' => 'https://api.openai.com/v1',
        'model' => 'gpt-4',
        'max_tokens' => 2000,
        'temperature' => 0.7
    ],
    'baidu' => [
        'api_key' => 'your-baidu-api-key',
        'secret_key' => 'your-baidu-secret-key',
        // ...
    ],
    'deepseek' => [
        'api_key' => 'your-deepseek-api-key',
        'base_url' => 'https://api.deepseek.com/v1',
        'model' => 'deepseek-chat'
    ],
    // 其他服务商配置...
];
```

### 4. Web服务器配置

#### Apache配置
确保启用了mod_rewrite模块，项目根目录的.htaccess文件已经配置好URL重写规则。

#### Nginx配置
在Nginx配置文件中添加：
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass 127.0.0.1:9000;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## 访问方式

### 本地开发环境

1. **使用PHP内置服务器**（推荐用于开发测试）：
```bash
cd /path/to/your/project
php -S localhost:8000
```
然后在浏览器访问：`http://localhost:8000`

2. **使用XAMPP/WAMP/MAMP**：
   - 将项目文件放到htdocs目录
   - 启动Apache和MySQL服务
   - 访问：`http://localhost/your-project-name`

### 生产环境

1. 将项目文件上传到Web服务器
2. 配置虚拟主机指向项目根目录
3. 确保数据库连接正常
4. 访问你的域名

## 主要页面

- **首页/智能对话**: `/` 或 `/ai/chat`
- **AI工具箱**: `/ai/tools`
- **会话管理**: `/ai/sessions`

## 目录结构

```
project/
├── app/Manage/              # 应用核心目录
│   ├── Config/             # 配置文件
│   ├── Controllers/        # 控制器
│   ├── Models/            # 数据模型
│   ├── Services/          # 业务服务
│   └── Views/             # 视图文件
├── database/              # 数据库文件
├── index.php             # 入口文件
├── .htaccess            # Apache重写规则
└── README.md           # 说明文档
```

## 常见问题

### 1. 页面显示404错误
- 检查Web服务器是否支持URL重写
- 确认.htaccess文件存在且配置正确
- 检查文件权限

### 2. 数据库连接失败
- 检查数据库配置信息是否正确
- 确认数据库服务是否启动
- 检查用户权限

### 3. AI接口调用失败
- 检查API密钥是否正确配置
- 确认网络连接正常
- 检查API服务商的配额和限制

### 4. 权限问题
- 确保Web服务器对项目目录有读取权限
- 检查PHP扩展是否完整安装

## 开发说明

本项目采用MVC架构模式：
- **Controller**: 处理HTTP请求和响应
- **Service**: 封装业务逻辑
- **Model**: 数据库操作
- **View**: 页面展示

## 技术栈

- **后端**: PHP 7.4+, PDO
- **前端**: Bootstrap 4, jQuery, SweetAlert2
- **数据库**: MySQL 5.7+
- **AI服务**: OpenAI GPT, 百度文心一言, 阿里通义千问, 智谱AI, DeepSeek

## 许可证

MIT License

## 支持

如有问题，请提交Issue或联系开发者。
