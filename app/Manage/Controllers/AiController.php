<?php

/**
 * AI功能控制器
 * 处理AI相关的HTTP请求和页面渲染
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class AiController extends BaseController
{
    private $aiService;
    private $chatHistoryModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->aiService = new AiService();
        $this->chatHistoryModel = new ChatHistoryModel();
    }

    /**
     * 首页
     */
    public function indexAction()
    {
        $viewData = [
            'title' => 'AI智能助手 - 首页',
            'breadcrumb' => ['首页'],
            'providers' => array_keys(AiConfig::PROVIDERS)
        ];

        $this->showView([
            'common/head',
            'ai/index',
            'common/footer'
        ], $viewData);
    }

    /**
     * AI聊天页面
     */
    public function chatAction()
    {
        $viewData = [
            'title' => 'AI智能助手',
            'breadcrumb' => ['AI功能', '智能助手'],
            'providers' => array_keys(AiConfig::PROVIDERS)
        ];
        
        $this->showView([
            'common/head',
            'ai/chat',
            'common/footer'
        ], $viewData);
    }
    
    /**
     * 处理AI聊天请求
     */
    public function sendMessageAction()
    {
        $message = trim($this->input->post('message'));
        $sessionId = $this->input->post('session_id');
        $provider = $this->input->post('provider', AiConfig::DEFAULT_PROVIDER);
        
        // 参数验证
        if (empty($message)) {
            $this->jsonReturn(['st' => 'error', 'msg' => '消息不能为空']);
        }
        
        if (strlen($message) > 2000) {
            $this->jsonReturn(['st' => 'error', 'msg' => '消息长度不能超过2000字符']);
        }
        
        // 获取当前用户ID
        $userId = $this->getCurrentUserId();
        
        // 创建AI服务实例
        $aiService = new AiService($provider);
        
        // 调用AI服务
        $result = $aiService->chat($message, $userId, $sessionId);
        
        $this->jsonReturn($result);
    }
    
    /**
     * 文本分析功能
     */
    public function analyzeTextAction()
    {
        $text = trim($this->input->post('text'));
        $type = $this->input->post('type', 'summary');
        $provider = $this->input->post('provider', AiConfig::DEFAULT_PROVIDER);
        
        // 参数验证
        if (empty($text)) {
            $this->jsonReturn(['st' => 'error', 'msg' => '文本内容不能为空']);
        }
        
        if (strlen($text) > 5000) {
            $this->jsonReturn(['st' => 'error', 'msg' => '文本长度不能超过5000字符']);
        }
        
        $allowedTypes = ['summary', 'sentiment', 'keywords', 'translate'];
        if (!in_array($type, $allowedTypes)) {
            $this->jsonReturn(['st' => 'error', 'msg' => '不支持的分析类型']);
        }
        
        // 获取当前用户ID
        $userId = $this->getCurrentUserId();
        
        // 创建AI服务实例
        $aiService = new AiService($provider);
        
        // 调用文本分析
        $result = $aiService->analyzeText($text, $type, $userId);
        
        $this->jsonReturn($result);
    }
    
    /**
     * 获取聊天历史记录
     */
    public function getHistoryAction()
    {
        $sessionId = $this->input->get('session_id');
        $userId = $this->getCurrentUserId();
        
        if (empty($sessionId)) {
            $this->jsonReturn(['st' => 'error', 'msg' => '会话ID不能为空']);
        }
        
        $history = $this->chatHistoryModel->getHistory($userId, $sessionId, 50);
        
        $this->jsonReturn([
            'st' => 'success',
            'data' => $history
        ]);
    }
    
    /**
     * 获取用户会话列表
     */
    public function getSessionsAction()
    {
        $page = (int)$this->input->get('page', 1);
        $pageSize = (int)$this->input->get('page_size', 20);
        $userId = $this->getCurrentUserId();
        
        $sessions = $this->chatHistoryModel->getUserSessions($userId, $page, $pageSize);
        
        $this->jsonReturn([
            'st' => 'success',
            'data' => $sessions
        ]);
    }
    
    /**
     * 删除会话
     */
    public function deleteSessionAction()
    {
        $sessionId = $this->input->post('session_id');
        $userId = $this->getCurrentUserId();
        
        if (empty($sessionId)) {
            $this->jsonReturn(['st' => 'error', 'msg' => '会话ID不能为空']);
        }
        
        $result = $this->chatHistoryModel->deleteSession($userId, $sessionId);
        
        if ($result) {
            $this->jsonReturn(['st' => 'success', 'msg' => '会话删除成功']);
        } else {
            $this->jsonReturn(['st' => 'error', 'msg' => '会话删除失败']);
        }
    }
    
    /**
     * AI工具页面
     */
    public function toolsAction()
    {
        $viewData = [
            'title' => 'AI工具箱',
            'breadcrumb' => ['AI功能', 'AI工具箱'],
            'providers' => array_keys(AiConfig::PROVIDERS)
        ];
        
        $this->showView([
            'common/head',
            'ai/tools',
            'common/footer'
        ], $viewData);
    }
    
    /**
     * 会话管理页面
     */
    public function sessionsAction()
    {
        $viewData = [
            'title' => '会话管理',
            'breadcrumb' => ['AI功能', '会话管理']
        ];
        
        $this->showView([
            'common/head',
            'ai/sessions',
            'common/footer'
        ], $viewData);
    }
    
    /**
     * 获取当前用户ID
     * @return int 用户ID
     */
    private function getCurrentUserId()
    {
        // 这里应该从session或其他地方获取当前登录用户的ID
        // 暂时返回固定值，实际项目中需要根据具体的用户认证机制来实现
        return $_SESSION['user_id'] ?? 1;
    }
}