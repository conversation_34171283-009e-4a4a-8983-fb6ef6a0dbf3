<?php

/**
 * 基础控制器类
 * 所有控制器的父类，提供公共方法
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class BaseController
{
    protected $input;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->input = new Input();
    }
    
    /**
     * 渲染视图
     * @param array $views 视图文件数组
     * @param array $data 传递给视图的数据
     */
    protected function showView($views, $data = [])
    {
        // 将数据提取为变量
        extract($data);
        
        // 渲染每个视图文件
        foreach ($views as $view) {
            $viewFile = MANAGE_PATH . '/Views/' . $view . '.php';
            if (file_exists($viewFile)) {
                include $viewFile;
            } else {
                echo "View file not found: " . $view;
            }
        }
    }
    
    /**
     * 返回JSON响应
     * @param array $data 响应数据
     */
    protected function jsonReturn($data)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 重定向
     * @param string $url 重定向URL
     */
    protected function redirect($url)
    {
        header("Location: " . $url);
        exit;
    }
    
    /**
     * 设置HTTP状态码
     * @param int $code 状态码
     */
    protected function setStatusCode($code)
    {
        http_response_code($code);
    }
}

/**
 * 输入处理类
 * 处理GET、POST等请求参数
 */
class Input
{
    /**
     * 获取POST参数
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function post($key, $default = null)
    {
        return isset($_POST[$key]) ? $this->sanitize($_POST[$key]) : $default;
    }
    
    /**
     * 获取GET参数
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get($key, $default = null)
    {
        return isset($_GET[$key]) ? $this->sanitize($_GET[$key]) : $default;
    }
    
    /**
     * 获取REQUEST参数
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function request($key, $default = null)
    {
        return isset($_REQUEST[$key]) ? $this->sanitize($_REQUEST[$key]) : $default;
    }
    
    /**
     * 数据清理
     * @param mixed $data 要清理的数据
     * @return mixed
     */
    private function sanitize($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        
        // 移除两端空白字符
        $data = trim($data);
        
        // 转换特殊字符为HTML实体
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        return $data;
    }
    
    /**
     * 获取原始POST数据
     * @return string
     */
    public function getRawInput()
    {
        return file_get_contents('php://input');
    }
    
    /**
     * 获取JSON格式的POST数据
     * @return array|null
     */
    public function getJsonInput()
    {
        $input = $this->getRawInput();
        return json_decode($input, true);
    }
}
