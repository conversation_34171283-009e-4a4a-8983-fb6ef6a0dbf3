<?php

/**
 * AI客户端基础类
 * 提供统一的AI服务调用接口
 */
class AiClient
{
    private $provider;
    private $config;
    private $httpClient;

    /**
     * 构造函数
     * @param string $provider AI服务提供商
     */
    public function __construct($provider = null)
    {
        $this->provider = $provider ?: AiConfig::DEFAULT_PROVIDER;
        $this->config = AiConfig::PROVIDERS[$this->provider];
        $this->httpClient = new HttpClient();
    }

    /**
     * 发送聊天请求
     * @param array $messages 消息数组
     * @param array $options 可选参数
     * @return array 响应结果
     */
    public function chat($messages, $options = [])
    {
        try {
            $requestData = $this->buildChatRequest($messages, $options);
            $headers = $this->buildHeaders();
            
            $response = $this->httpClient->post(
                $this->config['api_url'],
                $requestData,
                $headers
            );

            return $this->parseResponse($response);
        } catch (Exception $e) {
            return [
                'st' => 'error',
                'msg' => 'AI服务调用失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 构建聊天请求数据
     * @param array $messages 消息数组
     * @param array $options 可选参数
     * @return array 请求数据
     */
    private function buildChatRequest($messages, $options)
    {
        $data = [
            'model' => $options['model'] ?? $this->config['model'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? $this->config['max_tokens'],
            'temperature' => $options['temperature'] ?? $this->config['temperature']
        ];

        // 根据不同提供商调整请求格式
        switch ($this->provider) {
            case 'baidu':
                return $this->buildBaiduRequest($data);
            case 'ali':
                return $this->buildAliRequest($data);
            default:
                return $data;
        }
    }

    /**
     * 构建请求头
     * @return array 请求头数组
     */
    private function buildHeaders()
    {
        $headers = ['Content-Type: application/json'];
        
        switch ($this->provider) {
            case 'openai':
                $headers[] = 'Authorization: Bearer ' . $this->config['api_key'];
                break;
            case 'baidu':
                $headers[] = 'Authorization: Bearer ' . $this->getBaiduAccessToken();
                break;
            case 'ali':
                $headers[] = 'Authorization: Bearer ' . $this->config['api_key'];
                break;
        }

        return $headers;
    }

    /**
     * 解析响应结果
     * @param string $response 原始响应
     * @return array 解析后的结果
     */
    private function parseResponse($response)
    {
        $data = json_decode($response, true);
        
        if (!$data) {
            return [
                'st' => 'error',
                'msg' => '响应解析失败',
                'data' => null
            ];
        }

        // 根据不同提供商解析响应格式
        switch ($this->provider) {
            case 'openai':
                return $this->parseOpenAiResponse($data);
            case 'baidu':
                return $this->parseBaiduResponse($data);
            case 'ali':
                return $this->parseAliResponse($data);
            default:
                return [
                    'st' => 'error',
                    'msg' => '未知的AI服务提供商',
                    'data' => null
                ];
        }
    }

    /**
     * 解析OpenAI响应
     */
    private function parseOpenAiResponse($data)
    {
        if (isset($data['error'])) {
            return [
                'st' => 'error',
                'msg' => $data['error']['message'],
                'data' => null
            ];
        }

        return [
            'st' => 'success',
            'msg' => '请求成功',
            'data' => [
                'content' => $data['choices'][0]['message']['content'],
                'usage' => $data['usage'] ?? null
            ]
        ];
    }
}