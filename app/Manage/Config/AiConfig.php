<?php

/**
 * AI大模型配置文件
 * 支持多个AI服务提供商的配置
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class AiConfig
{
    // 默认使用的AI服务商
    const DEFAULT_PROVIDER = 'openai';
    
    // AI服务商配置
    const PROVIDERS = [
        'openai' => [
            'api_key' => 'your-openai-api-key',
            'base_url' => 'https://api.openai.com/v1',
            'model' => 'gpt-4',
            'max_tokens' => 2000,
            'temperature' => 0.7
        ],
        'baidu' => [
            'api_key' => 'your-baidu-api-key',
            'secret_key' => 'your-baidu-secret-key',
            'base_url' => 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
            'model' => 'ernie-bot-turbo'
        ],
        'aliyun' => [
            'api_key' => 'your-aliyun-api-key',
            'base_url' => 'https://dashscope.aliyuncs.com/api/v1',
            'model' => 'qwen-turbo'
        ],
        'zhipu' => [
            'api_key' => 'your-zhipu-api-key',
            'base_url' => 'https://open.bigmodel.cn/api/paas/v4',
            'model' => 'glm-4'
        ],
        'deepseek' => [
            'api_key' => 'your-deepseek-api-key',
            'base_url' => 'https://api.deepseek.com/v1',
            'model' => 'deepseek-chat',
            'max_tokens' => 2000,
            'temperature' => 0.7
        ]
    ];
    
    // 聊天历史记录保存天数
    const CHAT_HISTORY_DAYS = 30;
    
    // 单次对话最大轮数
    const MAX_CONVERSATION_ROUNDS = 20;
    
    // 请求超时时间（秒）
    const REQUEST_TIMEOUT = 30;
}