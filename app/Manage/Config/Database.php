<?php

/**
 * 数据库配置文件
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class Database
{
    // 数据库配置
    const DB_HOST = 'localhost';
    const DB_NAME = 'ai_chat';
    const DB_USER = 'root';
    const DB_PASS = 'Mysql#123';
    const DB_CHARSET = 'utf8mb4';
    
    private static $instance = null;
    private $connection = null;
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private function __construct()
    {
        $this->connect();
    }
    
    /**
     * 获取数据库实例（单例模式）
     * @return Database
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 连接数据库
     */
    private function connect()
    {
        try {
            $dsn = "mysql:host=" . self::DB_HOST . ";dbname=" . self::DB_NAME . ";charset=" . self::DB_CHARSET;
            $this->connection = new PDO($dsn, self::DB_USER, self::DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]);
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * 获取数据库连接
     * @return PDO
     */
    public function getConnection()
    {
        return $this->connection;
    }
    
    /**
     * 执行查询
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return PDOStatement
     */
    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 执行插入操作
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return int 插入的ID
     */
    public function insert($sql, $params = [])
    {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    /**
     * 执行更新/删除操作
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return int 影响的行数
     */
    public function execute($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction()
    {
        $this->connection->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit()
    {
        $this->connection->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback()
    {
        $this->connection->rollback();
    }
}
