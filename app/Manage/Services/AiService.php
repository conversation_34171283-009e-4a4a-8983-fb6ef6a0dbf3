<?php

/**
 * AI服务类
 * 提供统一的AI大模型调用接口，支持多个服务商
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class AiService
{
    private $provider;
    private $config;
    private $chatHistoryModel;
    
    /**
     * 构造函数
     * @param string $provider AI服务提供商
     */
    public function __construct($provider = null)
    {
        $this->provider = $provider ?: AiConfig::DEFAULT_PROVIDER;
        $this->config = AiConfig::PROVIDERS[$this->provider];
        $this->chatHistoryModel = new ChatHistoryModel();
    }
    
    /**
     * 发送聊天消息到AI模型
     * @param string $message 用户消息
     * @param int $userId 用户ID
     * @param string $sessionId 会话ID
     * @param array $context 上下文信息
     * @return array 返回AI响应结果
     */
    public function chat($message, $userId, $sessionId = null, $context = [])
    {
        try {
            // 生成会话ID
            if (!$sessionId) {
                $sessionId = $this->generateSessionId($userId);
            }
            
            // 获取历史对话
            $history = $this->getChatHistory($userId, $sessionId);
            
            // 构建消息数组
            $messages = $this->buildMessages($message, $history, $context);
            
            // 调用AI服务
            $response = $this->callAiProvider($messages);
            
            if ($response['st'] === 'success') {
                // 保存对话记录
                $this->saveChatHistory($userId, $sessionId, $message, $response['data']);
                
                return [
                    'st' => 'success',
                    'msg' => '调用成功',
                    'data' => [
                        'response' => $response['data'],
                        'session_id' => $sessionId,
                        'provider' => $this->provider
                    ]
                ];
            }
            
            return $response;
            
        } catch (Exception $e) {
            return [
                'st' => 'error',
                'msg' => 'AI服务调用失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 调用AI服务提供商
     * @param array $messages 消息数组
     * @return array 响应结果
     */
    private function callAiProvider($messages)
    {
        switch ($this->provider) {
            case 'openai':
                return $this->callOpenAI($messages);
            case 'baidu':
                return $this->callBaidu($messages);
            case 'aliyun':
                return $this->callAliyun($messages);
            case 'zhipu':
                return $this->callZhipu($messages);
            case 'deepseek':
                return $this->callDeepSeek($messages);
            default:
                throw new Exception('不支持的AI服务提供商: ' . $this->provider);
        }
    }
    
    /**
     * 调用OpenAI API
     * @param array $messages 消息数组
     * @return array 响应结果
     */
    private function callOpenAI($messages)
    {
        $data = [
            'model' => $this->config['model'],
            'messages' => $messages,
            'max_tokens' => $this->config['max_tokens'],
            'temperature' => $this->config['temperature']
        ];
        
        $response = $this->httpRequest(
            $this->config['base_url'] . '/chat/completions',
            $data,
            ['Authorization: Bearer ' . $this->config['api_key']]
        );
        
        if ($response['st'] === 'success') {
            $result = json_decode($response['data'], true);
            if (isset($result['choices'][0]['message']['content'])) {
                return [
                    'st' => 'success',
                    'data' => $result['choices'][0]['message']['content']
                ];
            }
        }
        
        return ['st' => 'error', 'msg' => 'OpenAI API调用失败'];
    }
    
    /**
     * 调用百度文心一言API
     * @param array $messages 消息数组
     * @return array 响应结果
     */
    private function callBaidu($messages)
    {
        // 获取access_token
        $accessToken = $this->getBaiduAccessToken();
        if (!$accessToken) {
            return ['st' => 'error', 'msg' => '获取百度access_token失败'];
        }
        
        $data = [
            'messages' => $messages,
            'temperature' => 0.7,
            'top_p' => 0.8
        ];
        
        $url = $this->config['base_url'] . '/chat/' . $this->config['model'] . '?access_token=' . $accessToken;
        $response = $this->httpRequest($url, $data);
        
        if ($response['st'] === 'success') {
            $result = json_decode($response['data'], true);
            if (isset($result['result'])) {
                return [
                    'st' => 'success',
                    'data' => $result['result']
                ];
            }
        }
        
        return ['st' => 'error', 'msg' => '百度API调用失败'];
    }
    
    /**
     * 获取百度access_token
     * @return string|false access_token或false
     */
    private function getBaiduAccessToken()
    {
        $url = 'https://aip.baidubce.com/oauth/2.0/token';
        $data = [
            'grant_type' => 'client_credentials',
            'client_id' => $this->config['api_key'],
            'client_secret' => $this->config['secret_key']
        ];
        
        $response = $this->httpRequest($url, $data, [], 'GET');
        if ($response['st'] === 'success') {
            $result = json_decode($response['data'], true);
            return $result['access_token'] ?? false;
        }
        
        return false;
    }
    
    /**
     * 调用阿里云通义千问API
     * @param array $messages 消息数组
     * @return array 响应结果
     */
    private function callAliyun($messages)
    {
        $data = [
            'model' => $this->config['model'],
            'input' => [
                'messages' => $messages
            ],
            'parameters' => [
                'temperature' => 0.7
            ]
        ];
        
        $response = $this->httpRequest(
            $this->config['base_url'] . '/services/aigc/text-generation/generation',
            $data,
            ['Authorization: Bearer ' . $this->config['api_key']]
        );
        
        if ($response['st'] === 'success') {
            $result = json_decode($response['data'], true);
            if (isset($result['output']['text'])) {
                return [
                    'st' => 'success',
                    'data' => $result['output']['text']
                ];
            }
        }
        
        return ['st' => 'error', 'msg' => '阿里云API调用失败'];
    }
    
    /**
     * 调用智谱AI API
     * @param array $messages 消息数组
     * @return array 响应结果
     */
    private function callZhipu($messages)
    {
        $data = [
            'model' => $this->config['model'],
            'messages' => $messages,
            'temperature' => 0.7
        ];
        
        $response = $this->httpRequest(
            $this->config['base_url'] . '/chat/completions',
            $data,
            ['Authorization: Bearer ' . $this->config['api_key']]
        );
        
        if ($response['st'] === 'success') {
            $result = json_decode($response['data'], true);
            if (isset($result['choices'][0]['message']['content'])) {
                return [
                    'st' => 'success',
                    'data' => $result['choices'][0]['message']['content']
                ];
            }
        }
        
        return ['st' => 'error', 'msg' => '智谱AI API调用失败'];
    }

    /**
     * 调用DeepSeek AI API
     * @param array $messages 消息数组
     * @return array 响应结果
     */
    private function callDeepSeek($messages)
    {
        $data = [
            'model' => $this->config['model'],
            'messages' => $messages,
            'max_tokens' => $this->config['max_tokens'],
            'temperature' => $this->config['temperature'],
            'stream' => false
        ];

        $response = $this->httpRequest(
            $this->config['base_url'] . '/chat/completions',
            $data,
            ['Authorization: Bearer ' . $this->config['api_key']]
        );

        if ($response['st'] === 'success') {
            $result = json_decode($response['data'], true);
            if (isset($result['choices'][0]['message']['content'])) {
                return [
                    'st' => 'success',
                    'data' => $result['choices'][0]['message']['content']
                ];
            }
        }

        return ['st' => 'error', 'msg' => 'DeepSeek AI API调用失败'];
    }
    
    /**
     * 构建消息数组
     * @param string $message 用户消息
     * @param array $history 历史对话
     * @param array $context 上下文信息
     * @return array 消息数组
     */
    private function buildMessages($message, $history, $context)
    {
        $messages = [
            ['role' => 'system', 'content' => '你是一个专业的AI助手，请用中文回答问题。']
        ];
        
        // 添加历史对话
        foreach ($history as $item) {
            $messages[] = ['role' => 'user', 'content' => $item['user_message']];
            $messages[] = ['role' => 'assistant', 'content' => $item['ai_response']];
        }
        
        // 添加当前消息
        $messages[] = ['role' => 'user', 'content' => $message];
        
        return $messages;
    }
    
    /**
     * HTTP请求封装
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @param array $headers 请求头
     * @param string $method 请求方法
     * @return array 响应结果
     */
    private function httpRequest($url, $data, $headers = [], $method = 'POST')
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, AiConfig::REQUEST_TIMEOUT);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } else {
            $url .= '?' . http_build_query($data);
            curl_setopt($ch, CURLOPT_URL, $url);
        }
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
            'Content-Type: application/json'
        ], $headers));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return ['st' => 'error', 'msg' => 'CURL错误: ' . $error];
        }
        
        if ($httpCode === 200) {
            return ['st' => 'success', 'data' => $response];
        } else {
            return ['st' => 'error', 'msg' => 'HTTP请求失败，状态码：' . $httpCode];
        }
    }
    
    /**
     * 生成会话ID
     * @param int $userId 用户ID
     * @return string 会话ID
     */
    private function generateSessionId($userId)
    {
        return 'chat_' . $userId . '_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }
    
    /**
     * 获取聊天历史记录
     * @param int $userId 用户ID
     * @param string $sessionId 会话ID
     * @return array 历史记录
     */
    private function getChatHistory($userId, $sessionId)
    {
        return $this->chatHistoryModel->getHistory($userId, $sessionId, 10);
    }
    
    /**
     * 保存聊天记录
     * @param int $userId 用户ID
     * @param string $sessionId 会话ID
     * @param string $userMessage 用户消息
     * @param string $aiResponse AI回复
     */
    private function saveChatHistory($userId, $sessionId, $userMessage, $aiResponse)
    {
        $this->chatHistoryModel->saveChat([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'user_message' => $userMessage,
            'ai_response' => $aiResponse,
            'provider' => $this->provider,
            'created_at' => date('Y-m-d H:i:s'),
            'creator' => $userId
        ]);
    }
    
    /**
     * 文本分析功能
     * @param string $text 待分析文本
     * @param string $type 分析类型
     * @param int $userId 用户ID
     * @return array 分析结果
     */
    public function analyzeText($text, $type, $userId)
    {
        $prompts = [
            'summary' => "请总结以下文本的主要内容，要求简洁明了：\n\n{$text}",
            'sentiment' => "请分析以下文本的情感倾向（正面/负面/中性），并说明理由：\n\n{$text}",
            'keywords' => "请提取以下文本的关键词，用逗号分隔：\n\n{$text}",
            'translate' => "请将以下文本翻译成英文：\n\n{$text}"
        ];
        
        $prompt = $prompts[$type] ?? $prompts['summary'];
        return $this->chat($prompt, $userId);
    }
}