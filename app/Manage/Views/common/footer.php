    </div>
    
    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 text-muted">
                &copy; <?= date('Y') ?> AI智能助手系统. 
                <span class="ml-2">
                    <i class="fas fa-heart text-danger"></i> 
                    Powered by AI Technology
                </span>
            </p>
        </div>
    </footer>
    
    <!-- j<PERSON><PERSON><PERSON> (Local) -->
    <script src="/public/js/jquery-3.6.4.min.js"></script>

    <!-- Bootstrap JS (Local) -->
    <script src="/public/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Moment.js -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/locale/zh-cn.js"></script>

    <!-- DateTimePicker -->
    <script src="https://cdn.jsdelivr.net/npm/tempusdominus-bootstrap-4@5.39.0/build/js/tempusdominus-bootstrap-4.min.js"></script>
    
    <!-- 公共JavaScript函数 -->
    <script>
    /**
     * 全局工具函数
     */
    window.Utils = {
        /**
         * 显示成功消息
         * @param {string} message 消息内容
         */
        showSuccess: function(message) {
            Swal.fire({
                icon: 'success',
                title: '成功',
                text: message,
                timer: 2000,
                showConfirmButton: false
            });
        },
        
        /**
         * 显示错误消息
         * @param {string} message 消息内容
         */
        showError: function(message) {
            Swal.fire({
                icon: 'error',
                title: '错误',
                text: message
            });
        },
        
        /**
         * 显示警告消息
         * @param {string} message 消息内容
         */
        showWarning: function(message) {
            Swal.fire({
                icon: 'warning',
                title: '警告',
                text: message
            });
        },
        
        /**
         * 显示确认对话框
         * @param {string} message 消息内容
         * @param {function} callback 确认后的回调函数
         */
        showConfirm: function(message, callback) {
            Swal.fire({
                title: '确认操作',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#007bff',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '确认',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed && typeof callback === 'function') {
                    callback();
                }
            });
        },
        
        /**
         * 显示加载中
         * @param {string} message 加载消息
         */
        showLoading: function(message = '处理中...') {
            Swal.fire({
                title: message,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        },
        
        /**
         * 关闭加载中
         */
        hideLoading: function() {
            Swal.close();
        },
        
        /**
         * 格式化日期时间
         * @param {string} datetime 日期时间字符串
         * @param {string} format 格式化模式
         * @returns {string}
         */
        formatDateTime: function(datetime, format = 'YYYY-MM-DD HH:mm:ss') {
            return moment(datetime).format(format);
        },
        
        /**
         * 复制文本到剪贴板
         * @param {string} text 要复制的文本
         */
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showSuccess('已复制到剪贴板');
                }).catch(() => {
                    this.fallbackCopyTextToClipboard(text);
                });
            } else {
                this.fallbackCopyTextToClipboard(text);
            }
        },
        
        /**
         * 备用复制方法
         * @param {string} text 要复制的文本
         */
        fallbackCopyTextToClipboard: function(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                this.showSuccess('已复制到剪贴板');
            } catch (err) {
                this.showError('复制失败，请手动复制');
            }
            
            document.body.removeChild(textArea);
        }
    };
    
    /**
     * 页面加载完成后的初始化
     */
    $(document).ready(function() {
        // 设置moment.js为中文
        moment.locale('zh-cn');
        
        // 初始化所有的tooltip
        $('[data-toggle="tooltip"]').tooltip();
        
        // 初始化所有的popover
        $('[data-toggle="popover"]').popover();
        
        // 为所有的Select2元素初始化
        $('.select2').select2({
            theme: 'bootstrap4',
            language: 'zh-CN'
        });
        
        // 设置AJAX全局配置
        $.ajaxSetup({
            beforeSend: function() {
                // 可以在这里添加全局的loading效果
            },
            complete: function() {
                // 可以在这里移除全局的loading效果
            },
            error: function(xhr, status, error) {
                if (xhr.status === 401) {
                    Utils.showError('登录已过期，请重新登录');
                    // 可以在这里跳转到登录页面
                } else if (xhr.status === 403) {
                    Utils.showError('没有权限执行此操作');
                } else if (xhr.status === 500) {
                    Utils.showError('服务器内部错误，请稍后重试');
                } else {
                    Utils.showError('网络错误，请检查网络连接');
                }
            }
        });
    });
    </script>

    <!-- 页面特定的JavaScript -->
    <?php
    $currentUri = $_SERVER['REQUEST_URI'];
    if (strpos($currentUri, '/ai/chat') !== false): ?>
        <script src="/public/js/chat.js"></script>
    <?php elseif (strpos($currentUri, '/ai/tools') !== false): ?>
        <script src="/public/js/tools.js"></script>
    <?php elseif (strpos($currentUri, '/ai/sessions') !== false): ?>
        <script src="/public/js/sessions.js"></script>
    <?php endif; ?>
</body>
</html>
