<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="fas fa-robot"></i> AI智能助手
                </h4>
            </div>
            <div class="card-body">
                <!-- 功能导航卡片 -->
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-comments fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">智能对话</h5>
                                <p class="card-text">与AI进行自然语言对话，获得智能回答和建议</p>
                                <a href="/ai/chat" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i> 开始对话
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-tools fa-3x text-success mb-3"></i>
                                <h5 class="card-title">AI工具箱</h5>
                                <p class="card-text">文本摘要、情感分析、关键词提取、中英翻译等实用工具</p>
                                <a href="/ai/tools" class="btn btn-success">
                                    <i class="fas fa-arrow-right"></i> 使用工具
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-history fa-3x text-info mb-3"></i>
                                <h5 class="card-title">会话管理</h5>
                                <p class="card-text">查看和管理你的AI对话历史记录</p>
                                <a href="/ai/sessions" class="btn btn-info">
                                    <i class="fas fa-arrow-right"></i> 查看历史
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速开始 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-rocket"></i> 快速开始
                                </h5>
                                <p class="card-text">选择一个AI模型开始你的智能体验：</p>

                                <div class="row">
                                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                                        <button class="btn btn-outline-primary btn-block quick-start" data-provider="openai">
                                            <i class="fab fa-openai"></i> OpenAI GPT
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                                        <button class="btn btn-outline-primary btn-block quick-start" data-provider="baidu">
                                            <i class="fas fa-brain"></i> 百度文心一言
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                                        <button class="btn btn-outline-primary btn-block quick-start" data-provider="aliyun">
                                            <i class="fas fa-cloud"></i> 阿里通义千问
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                                        <button class="btn btn-outline-primary btn-block quick-start" data-provider="zhipu">
                                            <i class="fas fa-lightbulb"></i> 智谱AI
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                                        <button class="btn btn-outline-primary btn-block quick-start" data-provider="deepseek">
                                            <i class="fas fa-robot"></i> DeepSeek
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-chart-line"></i> 使用统计
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h4 class="text-primary" id="totalChats">-</h4>
                                        <small class="text-muted">总对话数</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-success" id="todayChats">-</h4>
                                        <small class="text-muted">今日对话</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-info" id="activeSessions">-</h4>
                                        <small class="text-muted">活跃会话</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info-circle"></i> 系统信息
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><strong>系统版本:</strong> v1.0.0</li>
                                    <li><strong>支持模型:</strong> OpenAI, 百度, 阿里云, 智谱AI, DeepSeek</li>
                                    <li><strong>最后更新:</strong> <?= date('Y-m-d H:i:s') ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.quick-start {
    transition: all 0.3s ease;
}

.quick-start:hover {
    transform: scale(1.05);
}

.fa-3x {
    font-size: 3em;
}
</style>

<script>
$(document).ready(function() {
    /**
     * 初始化页面
     */
    function init() {
        loadStatistics();
        bindEvents();
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 快速开始按钮
        $('.quick-start').click(function() {
            const provider = $(this).data('provider');
            startQuickChat(provider);
        });
    }

    /**
     * 快速开始对话
     */
    function startQuickChat(provider) {
        // 跳转到聊天页面并设置默认模型
        window.location.href = `/ai/chat?provider=${provider}`;
    }

    /**
     * 加载使用统计
     */
    function loadStatistics() {
        // 这里可以调用API获取真实的统计数据
        // 暂时使用模拟数据
        setTimeout(function() {
            $('#totalChats').text('0');
            $('#todayChats').text('0');
            $('#activeSessions').text('0');
        }, 500);
    }

    // 初始化
    init();
});
</script>