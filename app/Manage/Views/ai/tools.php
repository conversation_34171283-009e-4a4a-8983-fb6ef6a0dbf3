<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">AI工具箱</h4>
                    <p class="card-text">提供多种AI文本处理工具</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 文本输入区域 -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="inputText">输入文本</label>
                                <textarea id="inputText" class="form-control" rows="12" placeholder="请输入要处理的文本内容..." maxlength="5000"></textarea>
                                <small class="text-muted">最多输入5000字符</small>
                            </div>
                            
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="toolType">处理类型</label>
                                        <select id="toolType" class="form-control">
                                            <option value="summary">文本摘要</option>
                                            <option value="sentiment">情感分析</option>
                                            <option value="keywords">关键词提取</option>
                                            <option value="translate">中英翻译</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="toolProvider">AI模型</label>
                                        <select id="toolProvider" class="form-control">
                                            <?php foreach ($providers as $provider): ?>
                                                <option value="<?= $provider ?>" <?= $provider === 'openai' ? 'selected' : '' ?>>
                                                    <?= ucfirst($provider) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <button id="processBtn" class="btn btn-primary btn-block">
                                <i class="fas fa-magic"></i> 开始处理
                            </button>
                        </div>
                        
                        <!-- 结果显示区域 -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>处理结果</label>
                                <div id="resultContainer" class="border rounded p-3" style="min-height: 300px; background-color: #f8f9fa;">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-robot fa-3x mb-3"></i>
                                        <p>请在左侧输入文本并选择处理类型，然后点击"开始处理"</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button id="copyBtn" class="btn btn-outline-secondary" disabled>
                                    <i class="fas fa-copy"></i> 复制结果
                                </button>
                                <button id="clearBtn" class="btn btn-outline-danger">
                                    <i class="fas fa-trash"></i> 清空
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 历史记录 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">处理历史</h5>
                </div>
                <div class="card-body">
                    <div id="historyContainer">
                        <div class="text-center text-muted">
                            <p>暂无处理历史</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.history-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
}

.history-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.history-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.history-type {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.history-time {
    color: #6c757d;
    font-size: 12px;
}

.history-content {
    margin-bottom: 10px;
}

.history-result {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- 工具箱功能JavaScript将在footer中加载 -->
