<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">
                    <i class="fas fa-history"></i> 会话管理
                </h4>
                <div>
                    <button id="refreshBtn" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button id="cleanupBtn" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash-alt"></i> 清理过期
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 搜索和筛选 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" id="searchInput" class="form-control" placeholder="搜索会话内容...">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="providerFilter" class="form-control">
                            <option value="">所有AI模型</option>
                            <option value="openai">OpenAI</option>
                            <option value="baidu">百度</option>
                            <option value="aliyun">阿里云</option>
                            <option value="zhipu">智谱AI</option>
                            <option value="deepseek">DeepSeek</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select id="dateFilter" class="form-control">
                            <option value="">所有时间</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                </div>
                
                <!-- 会话列表 -->
                <div id="sessionsContainer">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载会话列表...</p>
                    </div>
                </div>
                
                <!-- 分页 -->
                <nav id="paginationContainer" class="mt-4" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<style>
.session-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.session-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.session-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
}

.session-content {
    padding: 15px;
}

.session-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.provider-badge {
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
}

.message-preview {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 8px 0;
    border-left: 3px solid #007bff;
}

.message-preview.user {
    border-left-color: #28a745;
}

.message-preview.assistant {
    border-left-color: #007bff;
}

.session-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}
</style>

<!-- 会话管理功能JavaScript将在footer中加载 -->


