<div class="container-fluid">
    <div class="row">
        <!-- 左侧会话列表 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">会话列表</h5>
                    <button id="newChatBtn" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> 新对话
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="sessionList" class="list-group list-group-flush" style="max-height: 400px; overflow-y: auto;">
                        <!-- 会话列表将通过JS动态加载 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧聊天区域 -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">AI智能助手</h4>
                    <div class="d-flex align-items-center">
                        <label class="mb-0 mr-2">AI模型:</label>
                        <select id="providerSelect" class="form-control form-control-sm" style="width: 150px;">
                            <?php foreach ($providers as $provider): ?>
                                <option value="<?= $provider ?>" <?= $provider === 'openai' ? 'selected' : '' ?>>
                                    <?= ucfirst($provider) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 聊天区域 -->
                    <div id="chatContainer" class="chat-container mb-3" style="height: 450px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background-color: #fafafa;">
                        <div class="chat-message system">
                            <div class="message-content">
                                <i class="fas fa-robot text-primary"></i> 您好！我是AI助手，有什么可以帮助您的吗？
                            </div>
                        </div>
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="input-group">
                        <textarea id="messageInput" class="form-control" rows="3" placeholder="请输入您的问题..." maxlength="2000"></textarea>
                        <div class="input-group-append">
                            <button id="sendBtn" class="btn btn-primary" type="button">
                                <i class="fas fa-paper-plane"></i> 发送
                            </button>
                        </div>
                    </div>
                    <small class="text-muted">按 Ctrl+Enter 快速发送，最多输入2000字符</small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-container {
    border-radius: 8px;
}

.chat-message {
    margin-bottom: 15px;
    padding: 12px 15px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
}

.chat-message.user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
    margin-right: 0;
}

.chat-message.assistant {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    margin-left: 0;
    margin-right: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chat-message.system {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    text-align: center;
    margin: 0 auto;
    max-width: 60%;
}

.chat-message.loading {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-left: 0;
    margin-right: auto;
}

.session-item {
    cursor: pointer;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.session-item:hover {
    background-color: #f8f9fa;
}

.session-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.session-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.session-time {
    font-size: 12px;
    color: #6c757d;
}

.typing-indicator {
    display: inline-block;
}

.typing-indicator span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #999;
    animation: typing 1.4s infinite ease-in-out;
    margin-right: 2px;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}
</style>

<!-- 聊天功能JavaScript将在footer中加载 -->
