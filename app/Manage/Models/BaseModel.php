<?php

/**
 * 基础模型类
 * 所有模型的父类，提供数据库操作的基础方法
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class BaseModel
{
    protected $db;
    protected $table;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * 查询单条记录
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array|false
     */
    protected function fetchOne($sql, $params = [])
    {
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 查询多条记录
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array
     */
    protected function fetchAll($sql, $params = [])
    {
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 执行SQL语句
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return int 影响的行数
     */
    protected function execute($sql, $params = [])
    {
        return $this->db->execute($sql, $params);
    }
    
    /**
     * 插入数据
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return int 插入的ID
     */
    protected function insert($sql, $params = [])
    {
        return $this->db->insert($sql, $params);
    }
    
    /**
     * 根据ID查找记录
     * @param int $id 记录ID
     * @return array|false
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? AND deleted_at IS NULL";
        return $this->fetchOne($sql, [$id]);
    }
    
    /**
     * 查找所有记录
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll($limit = null, $offset = 0)
    {
        $sql = "SELECT * FROM {$this->table} WHERE deleted_at IS NULL ORDER BY id DESC";
        
        if ($limit !== null) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        return $this->fetchAll($sql);
    }
    
    /**
     * 根据条件查找记录
     * @param array $conditions 查询条件
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function findByConditions($conditions, $limit = null, $offset = 0)
    {
        $where = [];
        $params = [];
        
        foreach ($conditions as $field => $value) {
            $where[] = "{$field} = ?";
            $params[] = $value;
        }
        
        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' AND ', $where) . " AND deleted_at IS NULL ORDER BY id DESC";
        
        if ($limit !== null) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        return $this->fetchAll($sql, $params);
    }
    
    /**
     * 统计记录数
     * @param array $conditions 查询条件
     * @return int
     */
    public function count($conditions = [])
    {
        $where = ['deleted_at IS NULL'];
        $params = [];
        
        foreach ($conditions as $field => $value) {
            $where[] = "{$field} = ?";
            $params[] = $value;
        }
        
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE " . implode(' AND ', $where);
        $result = $this->fetchOne($sql, $params);
        
        return (int)$result['count'];
    }
    
    /**
     * 软删除记录
     * @param int $id 记录ID
     * @param int $deleterId 删除者ID
     * @return bool
     */
    public function softDelete($id, $deleterId)
    {
        $sql = "UPDATE {$this->table} SET deleted_at = ?, deleter = ? WHERE id = ?";
        return $this->execute($sql, [date('Y-m-d H:i:s'), $deleterId, $id]) > 0;
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction()
    {
        $this->db->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit()
    {
        $this->db->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback()
    {
        $this->db->rollback();
    }
}
