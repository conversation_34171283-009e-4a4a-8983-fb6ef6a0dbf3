<?php

/**
 * 聊天历史记录模型
 * 管理AI聊天的历史记录数据
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
class ChatHistoryModel extends BaseModel
{
    protected $table = 'chat_history';
    
    /**
     * 保存聊天记录
     * @param array $data 聊天数据
     * @return bool 保存结果
     */
    public function saveChat($data)
    {
        try {
            $sql = "INSERT INTO {$this->table} (user_id, session_id, user_message, ai_response, provider, created_at, creator) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $data['user_id'],
                $data['session_id'],
                $data['user_message'],
                $data['ai_response'],
                $data['provider'],
                $data['created_at'],
                $data['creator']
            ];
            
            return $this->execute($sql, $params);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取聊天历史记录
     * @param int $userId 用户ID
     * @param string $sessionId 会话ID
     * @param int $limit 限制条数
     * @return array 历史记录
     */
    public function getHistory($userId, $sessionId, $limit = 10)
    {
        $sql = "SELECT user_message, ai_response, created_at 
                FROM {$this->table} 
                WHERE user_id = ? AND session_id = ? AND deleted_at IS NULL
                ORDER BY created_at ASC 
                LIMIT ?";
        
        return $this->fetchAll($sql, [$userId, $sessionId, $limit]);
    }
    
    /**
     * 获取用户的会话列表
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $pageSize 每页条数
     * @return array 会话列表
     */
    public function getUserSessions($userId, $page = 1, $pageSize = 20)
    {
        $offset = ($page - 1) * $pageSize;
        
        $sql = "SELECT session_id, 
                       MIN(created_at) as start_time,
                       MAX(created_at) as last_time,
                       COUNT(*) as message_count,
                       SUBSTRING(MIN(user_message), 1, 50) as first_message
                FROM {$this->table} 
                WHERE user_id = ? AND deleted_at IS NULL
                GROUP BY session_id 
                ORDER BY last_time DESC 
                LIMIT ? OFFSET ?";
        
        return $this->fetchAll($sql, [$userId, $pageSize, $offset]);
    }
    
    /**
     * 删除会话记录
     * @param int $userId 用户ID
     * @param string $sessionId 会话ID
     * @return bool 删除结果
     */
    public function deleteSession($userId, $sessionId)
    {
        $sql = "UPDATE {$this->table} 
                SET deleted_at = ?, deleter = ? 
                WHERE user_id = ? AND session_id = ? AND deleted_at IS NULL";
        
        return $this->execute($sql, [date('Y-m-d H:i:s'), $userId, $userId, $sessionId]);
    }
    
    /**
     * 清理过期记录
     * @param int $days 保留天数
     * @return bool 清理结果
     */
    public function cleanExpiredRecords($days = 30)
    {
        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $sql = "UPDATE {$this->table} 
                SET deleted_at = ?, deleter = 0 
                WHERE created_at < ? AND deleted_at IS NULL";
        
        return $this->execute($sql, [date('Y-m-d H:i:s'), $expireDate]);
    }
}