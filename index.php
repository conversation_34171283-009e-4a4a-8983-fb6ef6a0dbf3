<?php
/**
 * 项目入口文件
 * 处理所有HTTP请求的路由分发
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 启动会话
session_start();

// 定义项目根目录
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('MANAGE_PATH', APP_PATH . '/Manage');

// 自动加载类文件
spl_autoload_register(function ($className) {
    $paths = [
        MANAGE_PATH . '/Controllers/' . $className . '.php',
        MANAGE_PATH . '/Models/' . $className . '.php',
        MANAGE_PATH . '/Services/' . $className . '.php',
        MANAGE_PATH . '/Libraries/' . $className . '.php',
        MANAGE_PATH . '/Config/' . $className . '.php',
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// 加载配置文件
require_once MANAGE_PATH . '/Config/Constants.php';
require_once MANAGE_PATH . '/Config/Database.php';

// 获取请求URI和方法
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// 移除查询字符串
$requestUri = strtok($requestUri, '?');

// 加载路由配置
$get = [];
$post = [];
require_once MANAGE_PATH . '/Config/RouteArray.php';

// 选择对应的路由数组
$routes = ($requestMethod === 'POST') ? $post : $get;

// 查找匹配的路由
$matchedRoute = null;
$controller = null;
$action = null;

foreach ($routes as $route => $config) {
    if ($config['uri'] === $requestUri) {
        $matchedRoute = $route;
        list($controller, $action) = explode('::', $route);
        break;
    }
}

// 如果没有找到匹配的路由，默认跳转到首页
if (!$matchedRoute) {
    if ($requestUri === '/' || $requestUri === '') {
        $controller = 'Ai';
        $action = 'index';
    } else {
        // 404错误
        http_response_code(404);
        echo "404 - Page Not Found";
        exit;
    }
}

// 实例化控制器并调用方法
try {
    $controllerClass = $controller . 'Controller';
    
    if (!class_exists($controllerClass)) {
        throw new Exception("Controller {$controllerClass} not found");
    }
    
    $controllerInstance = new $controllerClass();
    $actionMethod = $action . 'Action';
    
    if (!method_exists($controllerInstance, $actionMethod)) {
        throw new Exception("Action {$actionMethod} not found in {$controllerClass}");
    }
    
    // 调用控制器方法
    $controllerInstance->$actionMethod();
    
} catch (Exception $e) {
    // 错误处理
    http_response_code(500);
    echo "Error: " . $e->getMessage();
    error_log("Application Error: " . $e->getMessage());
}
