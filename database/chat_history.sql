-- AI聊天历史记录表
CREATE TABLE `chat_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `session_id` varchar(100) NOT NULL COMMENT '会话ID',
  `user_message` text NOT NULL COMMENT '用户消息',
  `ai_response` text NOT NULL COMMENT 'AI回复',
  `provider` varchar(50) NOT NULL DEFAULT 'openai' COMMENT 'AI服务提供商',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `creator` int(11) NOT NULL COMMENT '创建者',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updater` int(11) DEFAULT NULL COMMENT '更新者',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `deleter` int(11) DEFAULT NULL COMMENT '删除者',
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `idx_user_session` (`user_id`, `session_id`),
  <PERSON><PERSON>Y `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天历史记录表';