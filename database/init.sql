-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ai_chat` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ai_chat`;

-- AI聊天历史记录表
CREATE TABLE IF NOT EXISTS `chat_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `session_id` varchar(100) NOT NULL COMMENT '会话ID',
  `user_message` text NOT NULL COMMENT '用户消息',
  `ai_response` text NOT NULL COMMENT 'AI回复',
  `provider` varchar(50) NOT NULL DEFAULT 'openai' COMMENT 'AI服务提供商',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `creator` int(11) NOT NULL COMMENT '创建者',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updater` int(11) DEFAULT NULL COMMENT '更新者',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `deleter` int(11) DEFAULT NULL COMMENT '删除者',
  PRIMARY KEY (`id`),
  KEY `idx_user_session` (`user_id`, `session_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天历史记录表';

-- 用户表（简单示例）
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `creator` int(11) NOT NULL COMMENT '创建者',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updater` int(11) DEFAULT NULL COMMENT '更新者',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `deleter` int(11) DEFAULT NULL COMMENT '删除者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认用户
INSERT INTO `users` (`id`, `username`, `email`, `status`, `created_at`, `creator`) VALUES
(1, 'admin', '<EMAIL>', 1, NOW(), 1)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,int,bool,json',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `creator` int(11) NOT NULL COMMENT '创建者',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updater` int(11) DEFAULT NULL COMMENT '更新者',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `deleter` int(11) DEFAULT NULL COMMENT '删除者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `config_type`, `created_at`, `creator`) VALUES
('site_name', 'AI智能助手', '网站名称', 'string', NOW(), 1),
('site_description', '基于AI大模型的智能助手系统', '网站描述', 'string', NOW(), 1),
('default_ai_provider', 'openai', '默认AI服务商', 'string', NOW(), 1),
('max_chat_history', '50', '最大聊天历史记录数', 'int', NOW(), 1),
('chat_history_days', '30', '聊天记录保存天数', 'int', NOW(), 1)
ON DUPLICATE KEY UPDATE `config_value` = VALUES(`config_value`);
